import getQueryClient from '@/lib/getQueryClient'
import { queryUniqueKey } from '@/services/client'
import { getModuleData, getRelatedDict, queryPersonalInfo } from '@/services/server'
import { CommonUrlSearchParams } from '@/services/types'
import { HydrationBoundary, dehydrate } from '@tanstack/react-query'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import PageRoot from './PersonalInfo'

const Page = async (props: { searchParams: CommonUrlSearchParams }) => {
  const { searchParams } = props
  const channelCustomerId = cookies().get('Z_CHANNEL_CUSTOMER_ID')?.value
  const search = new URLSearchParams(searchParams).toString()
  if (!channelCustomerId) {
    const pathname = '/dashboard?' + search

    redirect(pathname)
  }

  const queryClient = getQueryClient()

  await queryClient.prefetchQuery({
    queryKey: ['PersonalInfo'],
    queryFn: queryPersonalInfo,
  })

  await queryClient.prefetchQuery({
    queryKey: ['UniqueKey'],
    queryFn: queryUniqueKey,
  })

  await queryClient.prefetchQuery({
    queryKey: ['getModuleData'],
    queryFn: () =>
      getModuleData({
        moduleCode: searchParams.module,
        clientId: searchParams.clientId,
      }),
  })
  await queryClient.prefetchQuery({
    queryKey: ['certiTypeDict'],
    queryFn: () => getRelatedDict(['certiType']),
  })

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <PageRoot />
    </HydrationBoundary>
  )
}

export default Page
